using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TeslaInventoryWatcher.Models;

namespace TeslaInventoryWatcher.Services
{
    public class InventoryMonitorService
    {
        private readonly TeslaInventoryClient _client;

        public InventoryMonitorService(TeslaInventoryClient client)
        {
            _client = client;
        }

        // Public so ViewModel can reuse parser for raw JSON -> VehicleItem list
        public static IReadOnlyList<VehicleItem> ParseItems(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return Array.Empty<VehicleItem>();

            try
            {
                using var doc = JsonDocument.Parse(json);
                var root = doc.RootElement;

                // Try common shapes: data.vehicles.results
                if (root.TryGetProperty("data", out var data) &&
                    data.TryGetProperty("vehicles", out var vehicles) &&
                    vehicles.TryGetProperty("results", out var resultsA) &&
                    resultsA.ValueKind == JsonValueKind.Array)
                {
                    return ParseVehiclesArray(resultsA);
                }

                // Or: results.vehicles
                if (root.TryGetProperty("results", out var resultsObj) &&
                    resultsObj.ValueKind == JsonValueKind.Object &&
                    resultsObj.TryGetProperty("vehicles", out var resultsVehicles) &&
                    resultsVehicles.ValueKind == JsonValueKind.Array)
                {
                    return ParseVehiclesArray(resultsVehicles);
                }

                // Or: flat results array
                if (root.TryGetProperty("results", out var flatResults) &&
                    flatResults.ValueKind == JsonValueKind.Array)
                {
                    return ParseVehiclesArray(flatResults);
                }

                return Array.Empty<VehicleItem>();
            }
            catch
            {
                return Array.Empty<VehicleItem>();
            }
        }

        private static IReadOnlyList<VehicleItem> ParseVehiclesArray(JsonElement arr)
        {
            var list = new List<VehicleItem>();
            foreach (var v in arr.EnumerateArray())
            {
                list.Add(new VehicleItem
                {
                    Id = GetProp(v, "VIN"),
                    Vin = GetProp(v, "VIN"),
                    Model = FirstNonEmpty(GetProp(v, "Model"), GetProp(v, "model"), GetProp(v, "TrimFamily")),
                    Trim = FirstNonEmpty(GetProp(v, "TrimName"), GetProp(v, "Trim")),
                    ExteriorColor = FirstNonEmpty(GetProp(v, "Paint"), GetProp(v, "PAINT")),
                    InteriorColor = FirstNonEmpty(GetProp(v, "Interior"), GetProp(v, "INTERIOR")),
                    Year = GetPropInt(v, "Year"),
                    PriceTry = FirstNonZeroDecimal(GetPropDecimal(v, "InventoryPrice"), GetPropDecimal(v, "Price")),
                    Location = CombineLocation(GetProp(v, "City"), GetProp(v, "State"), GetProp(v, "Country"), GetProp(v, "PickupLocation"))
                });
            }
            return list;
        }

        private static string CombineLocation(params string[] parts)
        {
            var sb = new System.Text.StringBuilder();
            foreach (var p in parts)
            {
                if (string.IsNullOrWhiteSpace(p)) continue;
                if (sb.Length > 0) sb.Append(", ");
                sb.Append(p.Trim());
            }
            return sb.ToString();
        }

        private static string FirstNonEmpty(params string[] values)
        {
            foreach (var v in values)
            {
                if (!string.IsNullOrWhiteSpace(v)) return v;
            }
            return string.Empty;
        }

        private static decimal FirstNonZeroDecimal(params decimal[] values)
        {
            foreach (var v in values)
            {
                if (v > 0) return v;
            }
            return 0m;
        }

        private static string GetProp(JsonElement obj, string name)
        {
            if (obj.ValueKind != JsonValueKind.Object) return string.Empty;
            if (obj.TryGetProperty(name, out var p))
            {
                try
                {
                    return p.ValueKind switch
                    {
                        JsonValueKind.String => p.GetString(),
                        JsonValueKind.Number => p.GetRawText(),
                        JsonValueKind.True => "true",
                        JsonValueKind.False => "false",
                        _ => string.Empty
                    } ?? string.Empty;
                }
                catch { return string.Empty; }
            }
            return string.Empty;
        }

        private static int GetPropInt(JsonElement obj, string name)
        {
            if (obj.ValueKind != JsonValueKind.Object) return 0;
            if (obj.TryGetProperty(name, out var p))
            {
                if (p.ValueKind == JsonValueKind.Number && p.TryGetInt32(out var i)) return i;
                if (p.ValueKind == JsonValueKind.String && int.TryParse(p.GetString(), out var si)) return si;
            }
            return 0;
        }

        private static decimal GetPropDecimal(JsonElement obj, string name)
        {
            if (obj.ValueKind != JsonValueKind.Object) return 0m;
            if (obj.TryGetProperty(name, out var p))
            {
                if (p.ValueKind == JsonValueKind.Number && p.TryGetDecimal(out var d)) return d;
                if (p.ValueKind == JsonValueKind.String && decimal.TryParse(p.GetString(), out var sd)) return sd;
            }
            return 0m;
        }

        // Hydration: run Playwright to obtain WAF cookies then retry client
        public async Task<string> TryHydrateHttpClientFromPlaywrightAndFetch(CancellationToken ct)
        {
            try
            {
                using var pw = await Microsoft.Playwright.Playwright.CreateAsync();
                await using var browser = await pw.Chromium.LaunchAsync(new Microsoft.Playwright.BrowserTypeLaunchOptions { Headless = true });

                var context = await browser.NewContextAsync(new Microsoft.Playwright.BrowserNewContextOptions
                {
                    Locale = "tr-TR",
                    TimezoneId = "Europe/Istanbul"
                });

                // Extra headers for fidelity; UA and CH hints will be extracted later
                await context.SetExtraHTTPHeadersAsync(new Dictionary<string, string>
                {
                    ["Accept-Language"] = "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
                    ["DNT"] = "1"
                });

                var page = await context.NewPageAsync();
                await page.GotoAsync("https://www.tesla.com/tr_tr/inventory/new/my", new Microsoft.Playwright.PageGotoOptions
                {
                    WaitUntil = Microsoft.Playwright.WaitUntilState.NetworkIdle,
                    Timeout = 60000
                });

                // Perform human-like interactions and wait for WAF cookies to be set
                var deadline = DateTime.UtcNow.AddSeconds(12);
                int settleIteration = 0;
                IReadOnlyList<Microsoft.Playwright.BrowserContextCookiesResult> cookies;
                do
                {
                    // minor interactions
                    await page.EvaluateAsync("() => window.scrollTo(0, document.body.scrollHeight)");
                    await page.WaitForTimeoutAsync(250);
                    await page.Mouse.MoveAsync(20 + (settleIteration * 5) % 200, 30 + (settleIteration * 7) % 150);
                    await page.WaitForTimeoutAsync(300);

                    cookies = await context.CookiesAsync();
                    if (ContainsWafCookies(cookies))
                        break;

                    settleIteration++;
                } while (DateTime.UtcNow < deadline);

                var ua = await page.EvaluateAsync<string>("() => navigator.userAgent");
                _client.SetCookiesFromPlaywright(cookies);
                _client.SetBrowserHeaders(ua);

                LogService.Info($"Hydrated HttpClient with {cookies.Count} cookies; retrying direct request.");
                // Retry direct client on same instance (cookies persisted)
                return await _client.GetTurkeyInventoryAsync(ct);
            }
            catch (Exception ex)
            {
                LogService.Warn($"Playwright hydration failed: {ex.Message}. Final fallback via page fetch.");
                try
                {
                    using var pw2 = await Microsoft.Playwright.Playwright.CreateAsync();
                    await using var browser2 = await pw2.Chromium.LaunchAsync(new Microsoft.Playwright.BrowserTypeLaunchOptions { Headless = true });
                    var context2 = await browser2.NewContextAsync(new Microsoft.Playwright.BrowserNewContextOptions
                    {
                        Locale = "tr-TR",
                        TimezoneId = "Europe/Istanbul"
                    });

                    var page2 = await context2.NewPageAsync();
                    await page2.GotoAsync("https://www.tesla.com/tr_tr/inventory/new/my", new Microsoft.Playwright.PageGotoOptions
                    {
                        WaitUntil = Microsoft.Playwright.WaitUntilState.NetworkIdle,
                        Timeout = 60000
                    });

                    // Execute coinorder GET within page context with credentials include
                    var script = @"async () => {
                        const params = {
                          model: 'my',
                          language: 'tr',
                          super_region: 'north america',
                          market: 'TR',
                          arrangeby: 'Price',
                          order: 'asc',
                          range: 0,
                          offset: 0,
                          count: 24,
                          isFalconDeliverySelectionEnabled: true,
                          version: 'v2'
                        };
                        const url = 'https://www.tesla.com/coinorder/api/v4/inventory-results?query=' + encodeURIComponent(JSON.stringify(params));
                        const res = await fetch(url, { credentials: 'include' });
                        return await res.text();
                    }";
                    var text = await page2.EvaluateAsync<string>(script);
                    return text ?? string.Empty;
                }
                catch (Exception ex2)
                {
                    LogService.Error($"Final page fetch fallback failed: {ex2.Message}");
                    return string.Empty;
                }
            }
        }

        private static bool ContainsWafCookies(IReadOnlyList<Microsoft.Playwright.BrowserContextCookiesResult> cookies)
        {
            if (cookies == null) return false;
            foreach (var c in cookies)
            {
                var name = c.Name ?? string.Empty;
                if (name.Equals("_abck", StringComparison.OrdinalIgnoreCase)) return true;
                if (name.Equals("bm_sz", StringComparison.OrdinalIgnoreCase)) return true;
                if (name.Equals("ak_bmsc", StringComparison.OrdinalIgnoreCase)) return true;
            }
            return false;
        }
    }
}
