{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"TeslaInventoryWatcher/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.4.0", "Hardcodet.NotifyIcon.Wpf": "2.0.1", "Microsoft.Playwright": "1.54.0", "System.Net.Http.Json": "9.0.7", "System.Text.Json": "9.0.7"}, "runtime": {"TeslaInventoryWatcher.dll": {}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "Hardcodet.NotifyIcon.Wpf/2.0.1": {"runtime": {"lib/net8.0-windows7.0/Hardcodet.NotifyIcon.Wpf.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.1.2717"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Playwright/1.54.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Text.Json": "9.0.7"}, "runtime": {"lib/netstandard2.0/Microsoft.Playwright.dll": {"assemblyVersion": "1.54.0.0", "fileVersion": "1.54.0.0"}}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Net.Http.Json/9.0.7": {"runtime": {"lib/net9.0/System.Net.Http.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "System.Text.Json/9.0.7": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}}}, "libraries": {"TeslaInventoryWatcher/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "Hardcodet.NotifyIcon.Wpf/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dtxmeZXzV2GzSm91aZ3hqzgoeVoARSkDPVCYfhVUNyyKBWYxMgNC0EcLiSYxD4Uc4alq/2qb3SmV8DgAENLRLQ==", "path": "hardcodet.notifyicon.wpf/2.0.1", "hashPath": "hardcodet.notifyicon.wpf.2.0.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Playwright/1.54.0": {"type": "package", "serviceable": true, "sha512": "sha512-u/ftvv1mdusX2R+4u0XINaRw10w9oGhxZePjkFgNZhpG/hUaOg/0npgavXh3mlO1u8b06v+XQKuvOYdsieHMZg==", "path": "microsoft.playwright/1.54.0", "hashPath": "microsoft.playwright.1.54.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Net.Http.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-6iEg+UUQYdEhptcYojluXo1vo+cFjsKEry/uhv+X3xUY6enDF0aTKeOaqTStYM5M8Tl4CqCz8+vQFqaJfQskuQ==", "path": "system.net.http.json/9.0.7", "hashPath": "system.net.http.json.9.0.7.nupkg.sha512"}, "System.Text.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "path": "system.text.json/9.0.7", "hashPath": "system.text.json.9.0.7.nupkg.sha512"}}}