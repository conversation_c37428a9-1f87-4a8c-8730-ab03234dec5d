using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace TeslaInventoryWatcher.Services
{
    // TeslaInventoryClient: coinorder-first GET with POST fallback.
    // Enhancements:
    // - Browser-fidelity headers including Origin and sec-ch-ua* client hints
    // - Dual-domain cookie injection for .tesla.com and www.tesla.com
    // - Jitter/backoff and HTML guard
    // Public API preserved:
    //   Task<string> GetTurkeyInventoryAsync(CancellationToken)
    //   void SetCookiesFromPlaywright(IEnumerable<dynamic>)
    //   void SetBrowserHeaders(string userAgent = null)
    public sealed class TeslaInventoryClient : IDisposable
    {
        private readonly HttpClient _http;
        private readonly CookieContainer _cookies;
        private readonly JsonSerializerOptions _jsonOptions;

        private string _userAgent =
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36";

        private static readonly Uri BaseCoinorder = new Uri("https://www.tesla.com/coinorder/api/v4/inventory-results");
        private static readonly Uri BaseInventoryPost = new Uri("https://www.tesla.com/inventory/api/v1/inventory-results");

        private static readonly Uri[] TrReferrers =
        {
            new Uri("https://www.tesla.com/tr_tr/inventory/new/my"),
            new Uri("https://www.tesla.com/tr_tr/inventory/new/m3"),
            new Uri("https://www.tesla.com/tr_tr/inventory/new/ms"),
            new Uri("https://www.tesla.com/tr_tr/inventory/new/mx")
        };

        private static readonly string[] AcceptLanguagePool =
        {
            "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
            "en-GB,en-US;q=0.9,en;q=0.8",
            "en-US,en;q=0.9"
        };

        public TeslaInventoryClient(HttpMessageHandler handler = null, TimeSpan? timeout = null)
        {
            _cookies = new CookieContainer();

            var httpHandler = handler as HttpClientHandler ?? new HttpClientHandler
            {
                AutomaticDecompression = DecompressionMethods.All,
                CookieContainer = _cookies,
                AllowAutoRedirect = true,
                UseCookies = true,
                UseProxy = true
            };

            _http = new HttpClient(httpHandler);
            _http.Timeout = timeout ?? TimeSpan.FromSeconds(60);

            _http.DefaultRequestHeaders.Accept.Clear();
            _http.DefaultRequestHeaders.Accept.ParseAdd("application/json, text/plain, */*");
            _http.DefaultRequestHeaders.CacheControl = new CacheControlHeaderValue { NoCache = true };
            _http.DefaultRequestHeaders.ConnectionClose = false;
            _http.DefaultRequestHeaders.Pragma.ParseAdd("no-cache");

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public void Dispose()
        {
            _http?.Dispose();
        }

        // Inject Playwright cookies. Signature preserved (dynamic to avoid direct Playwright dependency).
        public void SetCookiesFromPlaywright(IEnumerable<dynamic> playwrightCookies)
        {
            if (playwrightCookies == null) return;

            foreach (var c in playwrightCookies)
            {
                try
                {
                    string name = c.Name;
                    string value = c.Value;
                    string domain = NormalizeDomain(c.Domain);
                    string path = string.IsNullOrWhiteSpace((string)c.Path) ? "/" : (string)c.Path;
                    DateTime expires = TryToDateTime(c.Expires);

                    // Add cookie for both apex (.tesla.com) and host (www.tesla.com)
                    AddCookieForHosts(name, value, path, domain, expires, secure: true);
                }
                catch
                {
                    // ignore malformed
                }
            }
        }

        // Allow UA override from browser
        public void SetBrowserHeaders(string userAgent = null)
        {
            if (!string.IsNullOrWhiteSpace(userAgent))
            {
                _userAgent = userAgent.Trim();
            }
        }

        // Public API: returns raw JSON
        public async Task<string> GetTurkeyInventoryAsync(CancellationToken ct)
        {
            // 1) Coinorder GET attempts
            var getAttempts = 2;
            for (int i = 0; i < getAttempts; i++)
            {
                var referer = TrReferrers[i % TrReferrers.Length];
                try
                {
                    var getJson = await CoinorderGetAsync(referer, ct).ConfigureAwait(false);
                    if (!IsLikelyHtml(getJson))
                        return getJson;

                    await Task.Delay(Jitter(400, 900), ct).ConfigureAwait(false);
                }
                catch (HttpRequestException ex) when (IsTransient(ex) || IsForbidden(ex))
                {
                    await Task.Delay(Jitter(600, 1400), ct).ConfigureAwait(false);
                }
            }

            // 2) POST fallback attempts
            var postAttempts = 2;
            for (int i = 0; i < postAttempts; i++)
            {
                var referer = TrReferrers[(i + 1) % TrReferrers.Length];
                try
                {
                    var postJson = await InventoryPostAsync(referer, ct).ConfigureAwait(false);
                    if (!IsLikelyHtml(postJson))
                        return postJson;

                    await Task.Delay(Jitter(700, 1600), ct).ConfigureAwait(false);
                }
                catch (HttpRequestException ex) when (IsTransient(ex) || IsForbidden(ex))
                {
                    await Task.Delay(Jitter(900, 1800), ct).ConfigureAwait(false);
                }
            }

            throw new HttpRequestException("Tesla inventory: both GET coinorder and POST inventory-results returned HTML or failed.");
        }

        // --- GET coinorder ---

        private async Task<string> CoinorderGetAsync(Uri referer, CancellationToken ct)
        {
            var url = BuildCoinorderUrl();
            using var req = new HttpRequestMessage(HttpMethod.Get, url);

            ApplyBrowserHeaders(req, referer);

            using var resp = await _http.SendAsync(req, HttpCompletionOption.ResponseHeadersRead, ct).ConfigureAwait(false);
            await EnsureSuccessOrThrowAsync(resp, "coinorder GET", ct).ConfigureAwait(false);

            var body = await resp.Content.ReadAsStringAsync(ct).ConfigureAwait(false);
            return body;
        }

        private Uri BuildCoinorderUrl()
        {
            var queryObj = new
            {
                model = "my",
                language = "tr",
                super_region = "north america",
                market = "TR",
                arrangeby = "Price",
                order = "asc",
                range = 0,
                offset = 0,
                count = 24,
                isFalconDeliverySelectionEnabled = true,
                version = "v2"
            };

            var qpQuery = JsonSerializer.Serialize(queryObj);
            var builder = new StringBuilder(BaseCoinorder.ToString());
            builder.Append('?');
            builder.Append(Uri.EscapeDataString("query"));
            builder.Append('=');
            builder.Append(Uri.EscapeDataString(qpQuery));
            return new Uri(builder.ToString());
        }

        // --- POST fallback ---

        private async Task<string> InventoryPostAsync(Uri referer, CancellationToken ct)
        {
            using var req = new HttpRequestMessage(HttpMethod.Post, BaseInventoryPost);
            ApplyBrowserHeaders(req, referer);

            var payload = new
            {
                query = new
                {
                    model = "my",
                    language = "tr",
                    super_region = "north america",
                    market = "TR",
                    arrangeby = "Price",
                    order = "asc",
                    range = 0,
                    offset = 0,
                    count = 24,
                    isFalconDeliverySelectionEnabled = true,
                    version = "v2"
                }
            };

            var json = JsonSerializer.Serialize(payload, _jsonOptions);
            req.Content = new StringContent(json, Encoding.UTF8, "application/json");

            using var resp = await _http.SendAsync(req, HttpCompletionOption.ResponseHeadersRead, ct).ConfigureAwait(false);
            await EnsureSuccessOrThrowAsync(resp, "inventory POST", ct).ConfigureAwait(false);

            var body = await resp.Content.ReadAsStringAsync(ct).ConfigureAwait(false);
            return body;
        }

        // --- Headers & Cookies helpers ---

        private void ApplyBrowserHeaders(HttpRequestMessage req, Uri referer)
        {
            req.Headers.UserAgent.ParseAdd(_userAgent);
            req.Headers.Referrer = referer;

            // Accept typical of fetch/XHR
            req.Headers.Accept.Clear();
            req.Headers.Accept.ParseAdd("application/json, text/plain, */*");

            // ch client hints (defaults per user instruction)
            AddOrReplaceHeader(req, "sec-ch-ua", "\"Chromium\";v=\"126\", \"Not.A/Brand\";v=\"24\", \"Google Chrome\";v=\"126\"");
            AddOrReplaceHeader(req, "sec-ch-ua-mobile", "?0");
            AddOrReplaceHeader(req, "sec-ch-ua-platform", "\"Windows\"");

            // Origin + Fetch metadata
            AddOrReplaceHeader(req, "Origin", "https://www.tesla.com");
            AddOrReplaceHeader(req, "Sec-Fetch-Site", "same-origin");
            AddOrReplaceHeader(req, "Sec-Fetch-Mode", "cors");
            AddOrReplaceHeader(req, "Sec-Fetch-Dest", "empty");

            // Locale & encoding
            AddOrReplaceHeader(req, "Accept-Language", AcceptLanguagePool[Random.Shared.Next(AcceptLanguagePool.Length)]);
            AddOrReplaceHeader(req, "Accept-Encoding", "gzip, deflate, br, zstd");

            // Misc
            AddOrReplaceHeader(req, "DNT", "1");
            AddOrReplaceHeader(req, "Connection", "keep-alive");
            AddOrReplaceHeader(req, "Pragma", "no-cache");
            AddOrReplaceHeader(req, "Cache-Control", "no-cache");
        }

        private static void AddOrReplaceHeader(HttpRequestMessage req, string name, string value)
        {
            if (req.Headers.Contains(name))
                req.Headers.Remove(name);
            req.Headers.TryAddWithoutValidation(name, value);
        }

        private async Task EnsureSuccessOrThrowAsync(HttpResponseMessage resp, string label, CancellationToken ct)
        {
            if (resp.IsSuccessStatusCode)
                return;

            var status = (int)resp.StatusCode;

            if (status == 401 || status == 403)
            {
                string body = string.Empty;
                try { body = await resp.Content.ReadAsStringAsync(ct).ConfigureAwait(false); } catch { }
                throw new HttpRequestException($"{label}: {status} blocked. Body prefix: {Prefix(body)}");
            }

            resp.EnsureSuccessStatusCode();
        }

        private static string Prefix(string s, int max = 180)
        {
            if (string.IsNullOrEmpty(s)) return string.Empty;
            return s.Length <= max ? s : s.Substring(0, max);
        }

        private static bool IsTransient(HttpRequestException ex)
        {
            if (ex.InnerException is WebException) return true;
            var msg = ex.Message ?? string.Empty;
            if (msg.IndexOf("timeout", StringComparison.OrdinalIgnoreCase) >= 0) return true;
            if (msg.IndexOf("temporarily", StringComparison.OrdinalIgnoreCase) >= 0) return true;
            if (msg.IndexOf("reset", StringComparison.OrdinalIgnoreCase) >= 0) return true;
            return false;
        }

        private static bool IsForbidden(HttpRequestException ex)
        {
            var msg = ex.Message ?? string.Empty;
            return msg.Contains("403") || msg.IndexOf("Access Denied", StringComparison.OrdinalIgnoreCase) >= 0;
        }

        private static bool IsLikelyHtml(string body)
        {
            if (string.IsNullOrWhiteSpace(body)) return true;
            var s = body.TrimStart();
            if (s.Length == 0) return true;
            var head = s.Length <= 64 ? s : s.Substring(0, 64);
            return head.StartsWith("<!DOCTYPE html", StringComparison.OrdinalIgnoreCase)
                   || head.StartsWith("<html", StringComparison.OrdinalIgnoreCase)
                   || head.StartsWith("<body", StringComparison.OrdinalIgnoreCase)
                   || head.IndexOf("<title>", StringComparison.OrdinalIgnoreCase) >= 0;
        }

        private static string NormalizeDomain(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain)) return ".tesla.com";
            domain = domain.Trim();
            if (!domain.StartsWith(".")) domain = "." + domain;
            return domain;
        }

        private void AddCookieForHosts(string name, string value, string path, string domain, DateTime expires, bool secure)
        {
            // Normalize to .tesla.com and also add for www.tesla.com
            var apex = ".tesla.com";
            var host = "www.tesla.com";
            if (!domain.EndsWith("tesla.com", StringComparison.OrdinalIgnoreCase))
            {
                apex = domain;
                host = domain.TrimStart('.');
            }

            // Add for apex
            try
            {
                var c1 = new Cookie(name, value, string.IsNullOrWhiteSpace(path) ? "/" : path, apex)
                {
                    Secure = secure,
                    HttpOnly = false,
                    Expires = expires
                };
                _cookies.Add(new Uri("https://www.tesla.com"), c1);
            }
            catch { /* ignore */ }

            // Add for host
            try
            {
                var c2 = new Cookie(name, value, string.IsNullOrWhiteSpace(path) ? "/" : path, host)
                {
                    Secure = secure,
                    HttpOnly = false,
                    Expires = expires
                };
                _cookies.Add(new Uri("https://www.tesla.com"), c2);
            }
            catch { /* ignore */ }
        }

        private static DateTime TryToDateTime(object expires)
        {
            try
            {
                if (expires == null) return DateTime.UtcNow.AddDays(2);
                if (expires is double d)
                {
                    var epoch = DateTimeOffset.FromUnixTimeSeconds((long)d);
                    return epoch.UtcDateTime;
                }
                if (expires is long l)
                {
                    var epoch = DateTimeOffset.FromUnixTimeSeconds(l);
                    return epoch.UtcDateTime;
                }
                if (expires is string s && long.TryParse(s, out var ls))
                {
                    var epoch = DateTimeOffset.FromUnixTimeSeconds(ls);
                    return epoch.UtcDateTime;
                }
            }
            catch { }
            return DateTime.UtcNow.AddDays(2);
        }

        private static int Jitter(int minMs, int maxMs)
        {
            if (minMs < 0) minMs = 0;
            if (maxMs <= minMs) maxMs = minMs + 1;
            return minMs + Random.Shared.Next(maxMs - minMs);
        }
    }
}
