using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TeslaInventoryWatcher.Models;

namespace TeslaInventoryWatcher.Services;

public class BrowserMirrorClient
{
    private readonly HttpClient _http;
    private readonly MirrorConfig _cfg;

    public BrowserMirrorClient()
    {
        _cfg = MirrorConfig.Load();
        var handler = new HttpClientHandler
        {
            AutomaticDecompression = System.Net.DecompressionMethods.All
        };
        _http = new HttpClient(handler);
    }

    public async Task<IReadOnlyList<VehicleItem>> GetAsync(CancellationToken ct)
    {
        var url = _cfg.Endpoint;
        if (_cfg.Method.Equals("GET", StringComparison.OrdinalIgnoreCase))
        {
            var sep = url.Contains("?") ? "&" : "?";
            url = url + sep + "query=" + Uri.EscapeDataString(_cfg.Query);
        }

        using var req = new HttpRequestMessage(new HttpMethod(_cfg.Method), url);

        foreach (var kv in _cfg.Headers)
            req.Headers.TryAddWithoutValidation(kv.Key, kv.Value);

        if (!string.IsNullOrWhiteSpace(_cfg.Cookie))
            req.Headers.TryAddWithoutValidation("Cookie", _cfg.Cookie);

        if (_cfg.Method.Equals("POST", StringComparison.OrdinalIgnoreCase))
        {
            req.Content = new StringContent(_cfg.Query, Encoding.UTF8, "application/json");
        }

        var res = await _http.SendAsync(req, ct);
        res.EnsureSuccessStatusCode();
        await using var stream = await res.Content.ReadAsStreamAsync(ct);
        using var doc = await JsonDocument.ParseAsync(stream, cancellationToken: ct);
        return ParseResults(doc);
    }

    private static IReadOnlyList<VehicleItem> ParseResults(JsonDocument doc)
    {
        var list = new List<VehicleItem>();
        var root = doc.RootElement;

        // Prefer results.vehicles
        if (root.TryGetProperty("results", out var resultsObj) &&
            resultsObj.ValueKind == JsonValueKind.Object &&
            resultsObj.TryGetProperty("vehicles", out var vehiclesA) &&
            vehiclesA.ValueKind == JsonValueKind.Array)
        {
            foreach (var v in vehiclesA.EnumerateArray())
                list.Add(Map(v));
            return list;
        }

        // data.vehicles.results
        if (root.TryGetProperty("data", out var data) &&
            data.ValueKind == JsonValueKind.Object &&
            data.TryGetProperty("vehicles", out var vehiclesB) &&
            vehiclesB.ValueKind == JsonValueKind.Object &&
            vehiclesB.TryGetProperty("results", out var resultsB) &&
            resultsB.ValueKind == JsonValueKind.Array)
        {
            foreach (var v in resultsB.EnumerateArray())
                list.Add(Map(v));
            return list;
        }

        // flat results array
        if (root.TryGetProperty("results", out var flat) &&
            flat.ValueKind == JsonValueKind.Array)
        {
            foreach (var v in flat.EnumerateArray())
                list.Add(Map(v));
            return list;
        }

        return list;
    }

    private static VehicleItem Map(JsonElement v)
    {
        var item = new VehicleItem
        {
            Id = GetProp(v, "VIN"),
            Vin = GetProp(v, "VIN"),
            Model = FirstNonEmpty(GetProp(v, "Model"), GetProp(v, "model"), GetProp(v, "TrimFamily")),
            Trim = FirstNonEmpty(GetProp(v, "Trim"), GetProp(v, "TrimName")),
            ExteriorColor = FirstNonEmpty(GetProp(v, "PAINT"), GetProp(v, "Paint")),
            InteriorColor = FirstNonEmpty(GetProp(v, "INTERIOR"), GetProp(v, "Interior")),
            Year = GetPropInt(v, "Year"),
            PriceTry = FirstNonZeroDecimal(GetPropDecimal(v, "InventoryPrice"), GetPropDecimal(v, "Price")),
            Location = FirstNonEmpty(GetProp(v, "PickupLocation"),
                CombineLocation(GetProp(v, "City"), GetProp(v, "State"), GetProp(v, "Country")))
        };
        var modelForUrl = string.IsNullOrWhiteSpace(item.Model) ? "my" : item.Model.ToLowerInvariant();
        item.OrderUrl = $"https://www.tesla.com/tr_TR/inventory/new/{modelForUrl}";
        return item;
    }

    private static string CombineLocation(params string[] parts)
    {
        var sb = new StringBuilder();
        foreach (var p in parts)
        {
            if (string.IsNullOrWhiteSpace(p)) continue;
            if (sb.Length > 0) sb.Append(", ");
            sb.Append(p.Trim());
        }
        return sb.ToString();
    }

    private static string FirstNonEmpty(params string[] values)
    {
        foreach (var v in values)
        {
            if (!string.IsNullOrWhiteSpace(v)) return v;
        }
        return string.Empty;
    }

    private static decimal FirstNonZeroDecimal(params decimal[] values)
    {
        foreach (var v in values)
        {
            if (v > 0) return v;
        }
        return 0m;
    }

    private static string GetProp(JsonElement obj, string name)
    {
        if (obj.ValueKind != JsonValueKind.Object) return string.Empty;
        if (obj.TryGetProperty(name, out var p))
        {
            try
            {
                return p.ValueKind switch
                {
                    JsonValueKind.String => p.GetString(),
                    JsonValueKind.Number => p.GetRawText(),
                    JsonValueKind.True => "true",
                    JsonValueKind.False => "false",
                    _ => string.Empty
                } ?? string.Empty;
            }
            catch { return string.Empty; }
        }
        return string.Empty;
    }

    private static int GetPropInt(JsonElement obj, string name)
    {
        if (obj.ValueKind != JsonValueKind.Object) return 0;
        if (obj.TryGetProperty(name, out var p))
        {
            if (p.ValueKind == JsonValueKind.Number && p.TryGetInt32(out var i)) return i;
            if (p.ValueKind == JsonValueKind.String && int.TryParse(p.GetString(), out var si)) return si;
        }
        return 0;
    }

    private static decimal GetPropDecimal(JsonElement obj, string name)
    {
        if (obj.ValueKind != JsonValueKind.Object) return 0m;
        if (obj.TryGetProperty(name, out var p))
        {
            if (p.ValueKind == JsonValueKind.Number && p.TryGetDecimal(out var d)) return d;
            if (p.ValueKind == JsonValueKind.String && decimal.TryParse(p.GetString(), out var sd)) return sd;
        }
        return 0m;
    }

    private class MirrorConfig
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = "GET";
        public string Query { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
        public string Cookie { get; set; } = string.Empty;

        public static MirrorConfig Load()
        {
            var path = Path.Combine(AppContext.BaseDirectory, "TeslaInventorySettings.json");
            var json = File.ReadAllText(path);
            var cfg = JsonSerializer.Deserialize<MirrorConfig>(json, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new MirrorConfig();
            return cfg;
        }
    }
}
