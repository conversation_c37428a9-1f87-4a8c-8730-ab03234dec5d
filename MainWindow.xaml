<Window x:Class="TeslaInventoryWatcher.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TeslaInventoryWatcher"
        xmlns:viewModels="clr-namespace:TeslaInventoryWatcher.ViewModels"
        mc:Ignorable="d"
        Title="Tesla Inventory Watcher (Turkey)" Height="600" Width="1000">
    <Window.DataContext>
        <viewModels:MainViewModel/>
    </Window.DataContext>
    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibility"/>
    </Window.Resources>
    <DockPanel>
        <StatusBar DockPanel.Dock="Bottom">
            <StatusBarItem>
                <TextBlock Text="{Binding Status}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="Logs: see bottom panel"/>
            </StatusBarItem>
        </StatusBar>
        <ToolBar DockPanel.Dock="Top">
            <Button Content="Refresh" Command="{Binding RefreshCommand}"/>
            <Separator/>
            <TextBlock Text="Filter:" Margin="10,0,5,0"/>
            <TextBox Width="200" Text="{Binding Filter, UpdateSourceTrigger=PropertyChanged}"/>
            <Separator/>
            <CheckBox Content="Use Browser Mode" IsChecked="{Binding ForceBrowserMode}" Margin="10,0,0,0"/>
            <Separator/>
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="Status:" Margin="10,0,5,0"/>
                <ProgressBar Width="150" Height="16" IsIndeterminate="True" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisibility}}"/>
            </StackPanel>
        </ToolBar>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="150"/>
            </Grid.RowDefinitions>
            <DataGrid Grid.Row="0" ItemsSource="{Binding Vehicles}" AutoGenerateColumns="False" CanUserAddRows="False"
                  IsReadOnly="True" Margin="5">
            <DataGrid.Columns>
                <DataGridTextColumn Header="Model" Binding="{Binding Model}" Width="*"/>
                <DataGridTextColumn Header="Trim" Binding="{Binding Trim}" Width="2*"/>
                <DataGridTextColumn Header="Yıl" Binding="{Binding Year}" Width="Auto"/>
                <DataGridTextColumn Header="Dış Renk" Binding="{Binding ExteriorColor}" Width="Auto"/>
                <DataGridTextColumn Header="İç Renk" Binding="{Binding InteriorColor}" Width="Auto"/>
                <DataGridTextColumn Header="Fiyat (TRY)" Binding="{Binding PriceTry}" Width="Auto"/>
                <DataGridTextColumn Header="Konum" Binding="{Binding Location}" Width="2*"/>
                <DataGridTemplateColumn Header="Sipariş">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="Sipariş Ver" Command="{Binding DataContext.OpenOrderCommand, RelativeSource={RelativeSource AncestorType=Window}}" CommandParameter="{Binding}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            </DataGrid>
            <GroupBox Grid.Row="1" Header="Logs" Margin="5">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding Logs}" Name="LogsItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" FontFamily="Consolas" FontSize="12"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                        <ItemsControl.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="Copy Last Run Logs" Click="CopyLastRunLogs_Click"/>
                            </ContextMenu>
                        </ItemsControl.ContextMenu>
                    </ItemsControl>
                </ScrollViewer>
            </GroupBox>
        </Grid>
    </DockPanel>
</Window>
