{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\test1\\TeslaInventoryWatcher\\TeslaInventoryWatcher.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\test1\\TeslaInventoryWatcher\\TeslaInventoryWatcher.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\test1\\TeslaInventoryWatcher\\TeslaInventoryWatcher.csproj", "projectName": "TeslaInventoryWatcher", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\test1\\TeslaInventoryWatcher\\TeslaInventoryWatcher.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\test1\\TeslaInventoryWatcher\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Hardcodet.NotifyIcon.Wpf": {"target": "Package", "version": "[2.0.1, )"}, "Microsoft.Playwright": {"target": "Package", "version": "[1.54.0, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[9.0.7, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}