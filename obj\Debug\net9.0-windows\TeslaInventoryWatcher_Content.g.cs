﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("node.exe")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("license")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("api.json")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("install_media_pack.ps1")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_chrome_beta_linux.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_chrome_beta_mac.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_chrome_beta_win.ps1")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_chrome_stable_linux.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_chrome_stable_mac.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_chrome_stable_win.ps1")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_beta_linux.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_beta_mac.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_beta_win.ps1")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_dev_linux.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_dev_mac.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_dev_win.ps1")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_stable_linux.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_stable_mac.sh")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("reinstall_msedge_stable_win.ps1")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsers.json")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("cli.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.d.ts")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.mjs")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("androidserverimpl.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browserserverimpl.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("accessibility.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("android.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("api.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("artifact.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsercontext.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsertype.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("cdpsession.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("channelowner.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("clienthelper.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("clientinstrumentation.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("clientstacktrace.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("clock.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("connection.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("consolemessage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("coverage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("dialog.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("download.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("electron.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("elementhandle.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("errors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("eventemitter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("events.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("fetch.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("filechooser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("fileutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("frame.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("harrouter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("input.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("jshandle.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("jsonpipe.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("localutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("locator.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("network.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("page.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("platform.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwright.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("selectors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("stream.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("timeoutsettings.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("tracing.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("types.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("video.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("waiter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("weberror.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("websocket.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("worker.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("writablestream.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("driver.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("program.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("programwithteststub.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bindingscontrollersource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("clocksource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("injectedscriptsource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("pollingrecordersource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("storagescriptsource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("utilityscriptsource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("websocketmocksource.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("inprocess.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("inprocessfactory.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("outofprocess.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("serializers.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("validator.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("validatorprimitives.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwrightconnection.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwrightserver.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("accessibility.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("android.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("backendadb.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("artifact.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidibrowser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidichromium.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiconnection.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiexecutioncontext.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidifirefox.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiinput.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidinetworkmanager.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiovercdp.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidipage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidipdf.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidicommands.d.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidideserializer.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidikeyboard.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiprotocol.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiprotocolcore.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiprotocolpermissions.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("bidiserializer.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("firefoxprefs.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsercontext.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsertype.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("calllog.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("appicon.png")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("chromium.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("chromiumswitches.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("craccessibility.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crbrowser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crconnection.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crcoverage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crdevtools.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crdragdrop.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crexecutioncontext.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crinput.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crnetworkmanager.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crpage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crpdf.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crprotocolhelper.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crserviceworker.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("defaultfontfamilies.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocol.d.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("videorecorder.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("clock.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("csharp.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("java.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("javascript.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("jsonl.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("language.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("languages.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("python.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("types.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("console.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("cookiestore.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("debugcontroller.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("debugger.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("devicedescriptors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("devicedescriptorssource.json")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("dialog.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("androiddispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("artifactdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsercontextdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browserdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browsertypedispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("cdpsessiondispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("debugcontrollerdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("dialogdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("dispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("electrondispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("elementhandlerdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("framedispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("jshandledispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("jsonpipedispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("localutilsdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("networkdispatchers.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("pagedispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwrightdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("streamdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("tracingdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("websocketroutedispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("writablestreamdispatcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("dom.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("download.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("electron.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("loader.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("errors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("fetch.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("filechooser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("fileuploadutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffaccessibility.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffbrowser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffconnection.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffexecutioncontext.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffinput.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffnetworkmanager.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ffpage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("firefox.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocol.d.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("formdata.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("frames.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("frameselectors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("harbackend.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("harrecorder.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("hartracer.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("helper.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("input.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("instrumentation.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("javascript.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("launchapp.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("localutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("maceditingcommands.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("network.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("page.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("pipetransport.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwright.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("progress.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocolerror.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("recorder.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("chat.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("recorderapp.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("recorderrunner.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("recordersignalprocessor.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("recorderutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("throttledfile.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("browserfetcher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("dependencies.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("nativedeps.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("oopdownloadbrowsermain.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("screenshotter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("selectors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("socksclientcertificatesinterceptor.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("socksinterceptor.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("snapshotter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("snapshotterinjected.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("tracing.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("inmemorysnapshotter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("traceviewer.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("transport.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("types.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("uskeyboardlayout.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ascii.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("comparators.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("crypto.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("debug.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("debuglogger.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("env.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("eventshelper.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("expectutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("fileutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("happyeyeballs.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("hostplatform.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("httpserver.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("colorutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("compare.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("imagechannel.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("stats.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("linuxutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("network.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("nodeplatform.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("pipetransport.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("processlauncher.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("profiler.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("socksproxy.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("spawnasync.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("task.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("useragent.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wsserver.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("zipfile.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("zones.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocol.d.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("webkit.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkaccessibility.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkbrowser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkconnection.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkexecutioncontext.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkinput.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkinterceptablerequest.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkpage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkprovisionalpage.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("wkworkers.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("pixelmatch.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("utils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("utilsbundle.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("xdg-open")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("ariasnapshot.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("assert.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("colors.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("cssparser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("csstokenizer.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("headers.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("locatorgenerators.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("locatorparser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("locatorutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("manualpromise.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("mimetype.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("multimap.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocolformatter.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocolmetainfo.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("rtti.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("selectorparser.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("semaphore.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("stacktrace.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("stringutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("time.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("timeoutrunner.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("traceutils.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("types.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("urlmatch.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("utilityscriptserializers.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.html")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("codemirrormodule-c3utv-ge.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("codemirrormodule-ismgouzq.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("codicon-dcmgc-ay.ttf")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index-ds0ae8ml.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index-ehbmevry.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.html")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwright-logo.svg")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("codemirrormodule-3kz8gvgk.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("defaultsettingsview-ds-bson8.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("xtermmodule-boaieibi.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("codemirrormodule.c3utv-ge.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("codicon.dcmgc-ay.ttf")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("defaultsettingsview.nybt19ch.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.cfow-ezb.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.cg3bnedq.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("index.html")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwright-logo.svg")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("snapshot.html")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("sw.bundle.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("uimode.batfzhmg.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("uimode.bijnil3l.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("uimode.html")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("xtermmodule.beg8tuen.css")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("zipbundle.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("zipbundleimpl.js")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("package.json")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocol.yml")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("readme.md")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("thirdpartynotices.txt")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("protocol.d.ts")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("structs.d.ts")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("types.d.ts")]
[assembly: System.Windows.Resources.AssemblyAssociatedContentFileAttribute("playwright.ps1")]


