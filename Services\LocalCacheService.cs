using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using TeslaInventoryWatcher.Models;

namespace TeslaInventoryWatcher.Services;

public class LocalCacheService
{
    private readonly string _filePath;
    public LocalCacheService(string? filePath = null)
    {
        _filePath = filePath ?? Path.Combine(AppContext.BaseDirectory, "seen.json");
    }

    public HashSet<string> LoadSeenIds()
    {
        try
        {
            if (!File.Exists(_filePath)) return new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            var json = File.ReadAllText(_filePath);
            var list = JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
            return new HashSet<string>(list, StringComparer.OrdinalIgnoreCase);
        }
        catch
        {
            return new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        }
    }

    public void SaveSeenIds(HashSet<string> ids)
    {
        try
        {
            var json = JsonSerializer.Serialize(ids);
            File.WriteAllText(_filePath, json);
        }
        catch
        {
            // Ignore persistence errors
        }
    }
}
