﻿using System;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;

namespace TeslaInventoryWatcher;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        // Call the generated partial method; ensure the correct namespace matches the XAML x:Class
        InitializeComponent();
    }

#if DESIGN_TIME_STUB
    // Design-time/temporary build stub to satisfy compiler when generated partial not yet available.
    // Define DESIGN_TIME_STUB in project properties if you need to bypass missing generated InitializeComponent during interim builds.
    private void InitializeComponent() { }
#endif

    // Context menu action: copy only the logs visible in the log box (last run logs)
    private void CopyLastRunLogs_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var logsEnumerable = (this.DataContext as dynamic)?.Logs as System.Collections.IEnumerable;
            if (logsEnumerable == null)
            {
                Clipboard.SetText(string.Empty);
                return;
            }

            var sb = new StringBuilder();
            foreach (var line in logsEnumerable)
            {
                if (line is string s)
                    sb.AppendLine(s);
            }

            Clipboard.SetText(sb.ToString());
        }
        catch (Exception ex)
        {
            MessageBox.Show(this, "Failed to copy logs: " + ex.Message, "Copy Logs", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }
}