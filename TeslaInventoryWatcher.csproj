﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Hardcodet.NotifyIcon.Wpf" Version="2.0.1" />
    <PackageReference Include="Microsoft.Playwright" Version="1.54.0" />
    <PackageReference Include="System.Net.Http.Json" Version="9.0.7" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>

</Project>
