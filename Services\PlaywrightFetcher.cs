using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using TeslaInventoryWatcher.Models;

namespace TeslaInventoryWatcher.Services
{
    // Browser-mode inventory fetcher using Playwright.
    // Launches Chromium, navigates to TR inventory page, lets WAF cookies settle,
    // then performs a page-evaluated coinorder GET with credentials: 'include',
    // parses JSON, and returns a list of VehicleItem.
    public static class PlaywrightFetcher
    {
        // Public API preserved
        public static IReadOnlyList<VehicleItem> FetchTurkeyInventoryAsync(bool headless = true)
        {
            return FetchInternalAsync(headless).GetAwaiter().GetResult();
        }

        private static async Task<IReadOnlyList<VehicleItem>> FetchInternalAsync(bool headless)
        {
            try
            {
                using var pw = await Microsoft.Playwright.Playwright.CreateAsync();
                await using var browser = await pw.Chromium.LaunchAsync(new Microsoft.Playwright.BrowserTypeLaunchOptions
                {
                    Headless = headless
                });

                var context = await browser.NewContextAsync(new Microsoft.Playwright.BrowserNewContextOptions
                {
                    Locale = "tr-TR",
                    TimezoneId = "Europe/Istanbul"
                });

                await context.SetExtraHTTPHeadersAsync(new Dictionary<string, string>
                {
                    ["Accept-Language"] = "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
                    ["DNT"] = "1"
                });

                var page = await context.NewPageAsync();
                await page.GotoAsync("https://www.tesla.com/tr_tr/inventory/new/my", new Microsoft.Playwright.PageGotoOptions
                {
                    WaitUntil = Microsoft.Playwright.WaitUntilState.NetworkIdle,
                    Timeout = 60000
                });

                // Minor human-like interactions and waits to allow WAF cookies (_abck, bm_sz, ak_bmsc)
                var deadline = DateTime.UtcNow.AddSeconds(10);
                int settleIteration = 0;
                do
                {
                    await page.EvaluateAsync("() => window.scrollTo(0, document.body.scrollHeight)");
                    await page.WaitForTimeoutAsync(250);
                    await page.Mouse.MoveAsync(20 + (settleIteration * 5) % 200, 30 + (settleIteration * 7) % 150);
                    await page.WaitForTimeoutAsync(300);

                    var cookies = await context.CookiesAsync();
                    if (ContainsWafCookies(cookies))
                        break;

                    settleIteration++;
                } while (DateTime.UtcNow < deadline);

                // Perform same-origin fetch within the page to coinorder
                var script = @"async () => {
                    const params = {
                      model: 'my',
                      language: 'tr',
                      super_region: 'north america',
                      market: 'TR',
                      arrangeby: 'Price',
                      order: 'asc',
                      range: 0,
                      offset: 0,
                      count: 24,
                      isFalconDeliverySelectionEnabled: true,
                      version: 'v2'
                    };
                    const url = 'https://www.tesla.com/coinorder/api/v4/inventory-results?query=' + encodeURIComponent(JSON.stringify(params));
                    const res = await fetch(url, { credentials: 'include' });
                    const text = await res.text();
                    return text;
                }";

                var text = await page.EvaluateAsync<string>(script);
                if (string.IsNullOrWhiteSpace(text))
                    return Array.Empty<VehicleItem>();

                // Parse JSON safely and map
                try
                {
                    using var doc = JsonDocument.Parse(text);
                    var root = doc.RootElement;

                    // Support shapes: results.vehicles, data.vehicles.results, flat results
                    if (root.TryGetProperty("results", out var resultsObj) &&
                        resultsObj.ValueKind == JsonValueKind.Object &&
                        resultsObj.TryGetProperty("vehicles", out var vehiclesA) &&
                        vehiclesA.ValueKind == JsonValueKind.Array)
                    {
                        return ParseVehiclesArray(vehiclesA);
                    }

                    if (root.TryGetProperty("data", out var data) &&
                        data.ValueKind == JsonValueKind.Object &&
                        data.TryGetProperty("vehicles", out var vehiclesB) &&
                        vehiclesB.ValueKind == JsonValueKind.Object &&
                        vehiclesB.TryGetProperty("results", out var resultsB) &&
                        resultsB.ValueKind == JsonValueKind.Array)
                    {
                        return ParseVehiclesArray(resultsB);
                    }

                    if (root.TryGetProperty("results", out var flat) &&
                        flat.ValueKind == JsonValueKind.Array)
                    {
                        return ParseVehiclesArray(flat);
                    }

                    return Array.Empty<VehicleItem>();
                }
                catch
                {
                    return Array.Empty<VehicleItem>();
                }
            }
            catch (Exception ex)
            {
                LogService.Warn($"PlaywrightFetcher failed: {ex.Message}");
                return Array.Empty<VehicleItem>();
            }
        }

        private static bool ContainsWafCookies(IReadOnlyList<Microsoft.Playwright.BrowserContextCookiesResult> cookies)
        {
            if (cookies == null) return false;
            foreach (var c in cookies)
            {
                var name = c.Name ?? string.Empty;
                if (name.Equals("_abck", StringComparison.OrdinalIgnoreCase)) return true;
                if (name.Equals("bm_sz", StringComparison.OrdinalIgnoreCase)) return true;
                if (name.Equals("ak_bmsc", StringComparison.OrdinalIgnoreCase)) return true;
            }
            return false;
        }

        // Parsing helpers shared with other services for consistency
        private static IReadOnlyList<VehicleItem> ParseVehiclesArray(JsonElement arr)
        {
            var list = new List<VehicleItem>();
            foreach (var v in arr.EnumerateArray())
            {
                list.Add(new VehicleItem
                {
                    Id = GetProp(v, "VIN"),
                    Vin = GetProp(v, "VIN"),
                    Model = FirstNonEmpty(GetProp(v, "Model"), GetProp(v, "model"), GetProp(v, "TrimFamily")),
                    Trim = FirstNonEmpty(GetProp(v, "Trim"), GetProp(v, "TrimName")),
                    ExteriorColor = FirstNonEmpty(GetProp(v, "PAINT"), GetProp(v, "Paint")),
                    InteriorColor = FirstNonEmpty(GetProp(v, "INTERIOR"), GetProp(v, "Interior")),
                    Year = GetPropInt(v, "Year"),
                    PriceTry = FirstNonZeroDecimal(GetPropDecimal(v, "InventoryPrice"), GetPropDecimal(v, "Price")),
                    Location = FirstNonEmpty(GetProp(v, "PickupLocation"),
                        CombineLocation(GetProp(v, "City"), GetProp(v, "State"), GetProp(v, "Country")))
                });
            }
            return list;
        }

        private static string CombineLocation(params string[] parts)
        {
            var sb = new System.Text.StringBuilder();
            foreach (var p in parts)
            {
                if (string.IsNullOrWhiteSpace(p)) continue;
                if (sb.Length > 0) sb.Append(", ");
                sb.Append(p.Trim());
            }
            return sb.ToString();
        }

        private static string FirstNonEmpty(params string[] values)
        {
            foreach (var v in values)
            {
                if (!string.IsNullOrWhiteSpace(v)) return v;
            }
            return string.Empty;
        }

        private static decimal FirstNonZeroDecimal(params decimal[] values)
        {
            foreach (var v in values)
            {
                if (v > 0) return v;
            }
            return 0m;
        }

        private static string GetProp(JsonElement obj, string name)
        {
            if (obj.ValueKind != JsonValueKind.Object) return string.Empty;
            if (obj.TryGetProperty(name, out var p))
            {
                try
                {
                    return p.ValueKind switch
                    {
                        JsonValueKind.String => p.GetString(),
                        JsonValueKind.Number => p.GetRawText(),
                        JsonValueKind.True => "true",
                        JsonValueKind.False => "false",
                        _ => string.Empty
                    } ?? string.Empty;
                }
                catch { return string.Empty; }
            }
            return string.Empty;
        }

        private static int GetPropInt(JsonElement obj, string name)
        {
            if (obj.ValueKind != JsonValueKind.Object) return 0;
            if (obj.TryGetProperty(name, out var p))
            {
                if (p.ValueKind == JsonValueKind.Number && p.TryGetInt32(out var i)) return i;
                if (p.ValueKind == JsonValueKind.String && int.TryParse(p.GetString(), out var si)) return si;
            }
            return 0;
        }

        private static decimal GetPropDecimal(JsonElement obj, string name)
        {
            if (obj.ValueKind != JsonValueKind.Object) return 0m;
            if (obj.TryGetProperty(name, out var p))
            {
                if (p.ValueKind == JsonValueKind.Number && p.TryGetDecimal(out var d)) return d;
                if (p.ValueKind == JsonValueKind.String && decimal.TryParse(p.GetString(), out var sd)) return sd;
            }
            return 0m;
        }
    }
}
