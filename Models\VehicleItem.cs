using System;

namespace TeslaInventoryWatcher.Models;

public class VehicleItem
{
    public string Id { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string Trim { get; set; } = string.Empty;
    public string ExteriorColor { get; set; } = string.Empty;
    public string InteriorColor { get; set; } = string.Empty;
    public int Year { get; set; }
    public string Vin { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public decimal PriceTry { get; set; }
    public string Currency { get; set; } = "TRY";
    public string OrderUrl { get; set; } = string.Empty;
    public DateTimeOffset DiscoveredAt { get; set; } = DateTimeOffset.UtcNow;
}
