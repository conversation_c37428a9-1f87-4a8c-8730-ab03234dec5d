using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TeslaInventoryWatcher.Models;
using TeslaInventoryWatcher.Services;

namespace TeslaInventoryWatcher.ViewModels;

public partial class MainViewModel : ObservableObject
{
    private readonly InventoryMonitorService _monitor;

    [ObservableProperty]
    private string _status = "Ready";

    [ObservableProperty]
    private string _filter = string.Empty;

    public ObservableCollection<VehicleItem> Vehicles { get; } = new();
    public ObservableCollection<string> Logs { get; } = new();

    [ObservableProperty]
    private bool _isBusy;

    public IRelayCommand RefreshCommand { get; }
    public IRelayCommand<VehicleItem> OpenOrderCommand { get; }

    [ObservableProperty]
    private bool _forceBrowserMode;

    public MainViewModel()
    {
        var client = new TeslaInventoryClient();
        _monitor = new InventoryMonitorService(client);

        Services.LogService.LineAdded += l => App.Current.Dispatcher.Invoke(() => Logs.Add(l));

        RefreshCommand = new RelayCommand(async () => await ManualRefresh(), () => !IsBusy);
        OpenOrderCommand = new RelayCommand<VehicleItem>(OpenOrder);

        _ = ManualRefresh();
    }

    private void OnNewVehicles(System.Collections.Generic.IReadOnlyList<VehicleItem> newOnes)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            foreach (var v in newOnes)
                Vehicles.Insert(0, v);
            Status = $"Found {newOnes.Count} new vehicles at {DateTime.Now:t}";
        });
    }

    private async Task ManualRefresh()
    {
        if (IsBusy) return;
        IsBusy = true;
        RefreshCommand.NotifyCanExecuteChanged();
        try
        {
            Status = "Refreshing...";
            Services.LogService.Info("Refresh started");
            // If a user settings file exists, mirror browser request; else fallback
            System.Collections.Generic.IReadOnlyList<VehicleItem> items;
            var settingsPath = System.IO.Path.Combine(AppContext.BaseDirectory, "TeslaInventorySettings.json");
            if (ForceBrowserMode)
            {
                Services.LogService.Info("ForceBrowserMode is ON -> Using PlaywrightFetcher");
                items = await Task.Run(() => TeslaInventoryWatcher.Services.PlaywrightFetcher.FetchTurkeyInventoryAsync(headless: true));
            }
            else if (System.IO.File.Exists(settingsPath))
            {
                Services.LogService.Info("Using BrowserMirrorClient (settings file present)");
                var mirror = new TeslaInventoryWatcher.Services.BrowserMirrorClient();
                items = await Task.Run(() => mirror.GetAsync(default), default);
            }
            else
            {
                // Try direct API first; on failure hydrate HttpClient via Playwright, then final fallback to PlaywrightFetcher
                try
                {
                    Services.LogService.Info("Trying TeslaInventoryClient (direct API)");
                    var client = new TeslaInventoryClient();
                    var json = await client.GetTurkeyInventoryAsync(default);
                    items = InventoryMonitorService.ParseItems(json);
                    if (items.Count == 0)
                        throw new Exception("Empty results");
                }
                catch (Exception ex)
                {
                    Services.LogService.Warn($"Direct API failed: {ex.Message}");
                    Status = "Hydrating session via embedded browser...";
                    // Hydration attempt: inject cookies/UA into client and retry direct API
                    var hydratedJson = await new InventoryMonitorService(new TeslaInventoryClient())
                        .TryHydrateHttpClientFromPlaywrightAndFetch(default);
                    items = InventoryMonitorService.ParseItems(hydratedJson);

                    if (items.Count == 0)
                    {
                        Status = "Fetching via embedded browser (Playwright)...";
                        Services.LogService.Info("Fallback to PlaywrightFetcher");
                        items = await Task.Run(() => TeslaInventoryWatcher.Services.PlaywrightFetcher.FetchTurkeyInventoryAsync(headless: true));
                    }
                }
            }
            Vehicles.Clear();
            if (items.Count == 0)
            {
                Status = "Tesla API returned no items (possibly 403). Please try again.";
                Services.LogService.Warn("No items returned");
                return;
            }
            foreach (var i in items.OrderByDescending(v => v.DiscoveredAt))
                Vehicles.Add(i);
            Status = $"Loaded {Vehicles.Count} vehicles";
            Services.LogService.Info($"Refresh completed with {Vehicles.Count} vehicles");
        }
        catch (Exception ex)
        {
            Status = $"Error: {ex.Message}";
            Services.LogService.Exception(ex, "ManualRefresh error");
        }
        finally
        {
            IsBusy = false;
            RefreshCommand.NotifyCanExecuteChanged();
        }
    }

    private void OpenOrder(VehicleItem? item)
    {
        if (item == null || string.IsNullOrWhiteSpace(item.OrderUrl)) return;
        try
        {
            Process.Start(new ProcessStartInfo
            {
                FileName = item.OrderUrl,
                UseShellExecute = true
            });
        }
        catch (Exception ex)
        {
            Status = $"Unable to open link: {ex.Message}";
        }
    }
}
