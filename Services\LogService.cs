using System;
using System.Collections.Concurrent;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace TeslaInventoryWatcher.Services;

public static class LogService
{
    private static readonly object _fileLock = new();
    private static readonly ConcurrentQueue<string> _recent = new();
    public static event Action<string>? LineAdded;

    public static string LogsDirectory => Path.Combine(AppContext.BaseDirectory, "Logs");

    private static string CurrentLogFile => Path.Combine(LogsDirectory, $"app-{DateTime.Now:yyyyMMdd}.log");

    public static void Info(string message) => Write("INFO", message);
    public static void Error(string message) => Write("ERROR", message);
    public static void Warn(string message) => Write("WARN", message);

    public static void Exception(Exception ex, string? context = null)
        => Write("ERROR", (context != null ? context + ": " : "") + ex.ToString());

    private static void Write(string level, string message)
    {
        var line = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [{level}] {message}";
        _recent.Enqueue(line);
        while (_recent.Count > 500 && _recent.TryDequeue(out _)) { }
        try
        {
            Directory.CreateDirectory(LogsDirectory);
            lock (_fileLock)
            {
                File.AppendAllText(CurrentLogFile, line + Environment.NewLine, Encoding.UTF8);
            }
        }
        catch { /* ignore file I/O errors */ }
        try
        {
            LineAdded?.Invoke(line);
        }
        catch { /* ignore UI subscribers errors */ }
    }
}
