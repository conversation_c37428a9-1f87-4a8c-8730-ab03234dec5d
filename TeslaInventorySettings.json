{"endpoint": "https://www.tesla.com/coinorder/api/v4/inventory-results", "method": "GET", "query": "{\"query\":{\"model\":\"my\",\"condition\":\"new\",\"options\":{},\"arrangeby\":\"Price\",\"order\":\"asc\",\"market\":\"TR\",\"language\":\"tr\",\"super_region\":\"north america\",\"lng\":\"\",\"lat\":\"\",\"zip\":\"\",\"range\":0},\"offset\":0,\"count\":24,\"outsideOffset\":0,\"outsideSearch\":false,\"isFalconDeliverySelectionEnabled\":true,\"version\":\"v2\"}", "headers": {"accept": "*/*", "accept-language": "en-GB,en-US;q=0.9,en;q=0.8", "priority": "u=1, i", "referer": "https://www.tesla.com/tr_TR/inventory/new/my", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "same-origin", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "cookie": "REPLACE_WITH_YOUR_COOKIE"}